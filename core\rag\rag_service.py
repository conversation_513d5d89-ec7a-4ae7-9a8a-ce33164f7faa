from abc import ABC, abstractmethod
from dataclasses import dataclass, field


@dataclass
class FileUploadResult:
    success: bool


class RAGServiceBase(ABC):
    """
    保存文件信息到数据
    """

    def _save_file(self, *, user_id: str, file_id: str, file_name: str):
        pass

    """
  
  """

    def upload_file(self, *, user_id: str, file_content: bytes, file_name: str):
        self.upload_file_to_rag_service(file_content, file_name)

    """
  上传文件
  """

    @abstractmethod
    def upload_file_to_rag_service(
        self, *, file_content: bytes, file_name: str
    ) -> dict:
        pass

    """
  解析文件
  """

    @abstractmethod
    def parse_file(self, file_id: str):
        pass

    """
  查询文件状态 上传成功 等待解析 解析完成 解析失败
  """

    @abstractmethod
    def get_file_status(self, file_id: str) -> dict:
        pass

    """
  删除文件
  """

    @abstractmethod
    def delete_file(self, user_id: str, file_id: int) -> bool:
        pass

    """
  召回文件内容
  召回时验证是否当前用户的文件
  """

    def retrieve_from_documents(
        self, query: str, user_id: str, file_ids: list[int]
    ) -> list[dict]:
        pass
